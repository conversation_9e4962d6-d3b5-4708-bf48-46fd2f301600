# TCP客户端压力测试 - 使用说明

## 简单说明

这个工具可以帮你自动运行多个测试用例，每个测试用例会启动不同数量的客户端，发送不同大小的消息。

## 使用步骤

### 1. 编译项目
```bash
# 给脚本执行权限
chmod +x *.sh

# 编译项目
./build.sh
```

### 2. 确保服务器运行
确保你的TCP服务器在 127.0.0.1:8888 上运行

### 3. 先简单测试（推荐）
```bash
# 运行简单测试，验证功能是否正常
./simple_test.sh
```

### 4. 运行完整测试
```bash
# 运行完整测试脚本
./run_tests.sh
```

## 脚本说明

### simple_test.sh
- 超简单的测试脚本
- 先测试1个客户端10秒
- 再测试3个客户端10秒
- 用来验证功能是否正常

### run_tests.sh  
- 完整的测试脚本
- 有5个选项：
  1. 快速测试（1和3个客户端，各10秒）
  2. 运行所有测试用例（13个测试，每个3分钟）
  3. 运行指定测试用例
  4. 自定义测试
  5. 退出

## 测试用例说明

每个测试用例的格式是：
- **客户端数量**：同时启动几个客户端程序
- **消息大小**：每次发送多少字节的数据  
- **发送间隔**：多久发送一次消息（微秒）
- **持续时间**：测试跑多长时间

比如："5个客户端，128字节，10微秒间隔，3分钟"
意思是：同时启动5个客户端程序，每个客户端每隔10微秒发送一次128字节的消息，总共跑3分钟。

## 查看结果

测试完成后，结果会保存在 `test_results/` 目录下：
- 每个测试用例有自己的文件夹
- 每个客户端有自己的日志文件

## 手动测试单个客户端

如果你想手动测试一个客户端：
```bash
./build/tcp_qt5 --headless --client-name test1 --test-duration 10 --send-interval 1000 --message-size 128
```

参数说明：
- `--headless`: 无界面模式
- `--client-name`: 客户端名称
- `--test-duration`: 测试持续时间（秒）
- `--send-interval`: 发送间隔（微秒）
- `--message-size`: 消息大小（字节）

## 常见问题

1. **编译失败**：检查是否安装了Qt5和cmake
2. **连接失败**：检查服务器是否在运行
3. **权限错误**：运行 `chmod +x *.sh` 给脚本执行权限

## 推荐使用流程

1. 先运行 `./build.sh` 编译
2. 确保服务器运行
3. 运行 `./simple_test.sh` 验证功能
4. 如果正常，运行 `./run_tests.sh` 选择"快速测试"
5. 如果快速测试正常，再运行完整测试
