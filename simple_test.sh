#!/bin/bash
# 超简单的测试脚本

echo "TCP客户端简单测试"
echo "=================="



echo "请确保TCP服务器在 127.0.0.1:8888 运行"
echo ""

# 测试1: 单个客户端，10秒
echo "测试1: 1个客户端，128字节，1000微秒间隔，10秒"
./build/tcp_qt5 --headless --client-name test1 --test-duration 10 --send-interval 1000 --message-size 128 --server 127.0.0.1 --port 8888

echo ""
echo "等待3秒..."
sleep 3

# 测试2: 3个客户端，10秒
echo "测试2: 3个客户端，128字节，1000微秒间隔，10秒"

# 创建结果目录
mkdir -p test_results

# 启动3个客户端（后台运行）
./build/tcp_qt5 --headless --client-name test2_1 --test-duration 10 --send-interval 1000 --message-size 128 --server 127.0.0.1 --port 8888 > test_results/test2_1.log 2>&1 &
./build/tcp_qt5 --headless --client-name test2_2 --test-duration 10 --send-interval 1000 --message-size 128 --server 127.0.0.1 --port 8888 > test_results/test2_2.log 2>&1 &
./build/tcp_qt5 --headless --client-name test2_3 --test-duration 10 --send-interval 1000 --message-size 128 --server 127.0.0.1 --port 8888 > test_results/test2_3.log 2>&1 &

echo "启动了3个客户端，等待10秒完成..."
sleep 11

echo ""
echo "测试完成！日志文件在 test_results/ 目录"
echo "如果测试正常，可以运行: ./run_tests.sh 进行完整测试"
