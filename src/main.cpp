#include "mainwindow.h"

#include <QApplication>
#include <QCommandLineParser>
#include <QCommandLineOption>
#include <QStringList>
#include <QString>
#include <QDebug>
#include <QTimer>
#include <QDir>

/**
 * 主函数 - 支持命令行参数的TCP客户端应用程序
 * 支持的命令行参数:
 * --config: 配置文件路径
 * --client-name: 客户端名称
 * --test-duration: 测试持续时间(秒)
 * --send-interval: 发送间隔(微秒)
 * --headless: 无界面模式(用于压力测试)
 */
int main(int argc, char *argv[])
{
    QApplication a(argc, argv);
    
    // 设置应用程序信息
    a.setApplicationName("TCP Client");
    a.setApplicationVersion("1.0");
    a.setOrganizationName("Test Organization");
    
    // 创建命令行解析器
    QCommandLineParser parser;
    parser.setApplicationDescription("TCP客户端压力测试工具");
    parser.addHelpOption();
    parser.addVersionOption();
    
    // 定义命令行选项
    QCommandLineOption configOption(
        QStringList() << "c" << "config",
        "配置文件路径 (默认: config.yaml)",
        "config-file",
        "config.yaml"
    );
    parser.addOption(configOption);
    
    QCommandLineOption clientNameOption(
        QStringList() << "n" << "client-name",
        "客户端名称 (用于标识不同的客户端实例)",
        "client-name",
        "default_client"
    );
    parser.addOption(clientNameOption);
    
    QCommandLineOption testDurationOption(
        QStringList() << "d" << "test-duration",
        "测试持续时间(秒) (0表示无限制，默认: 0)",
        "seconds",
        "0"
    );
    parser.addOption(testDurationOption);
    
    QCommandLineOption sendIntervalOption(
        QStringList() << "i" << "send-interval",
        "消息发送间隔(微秒) (默认: 1000)",
        "microseconds",
        "1000"
    );
    parser.addOption(sendIntervalOption);
    
    QCommandLineOption headlessOption(
        QStringList() << "headless",
        "无界面模式 (用于压力测试，不显示GUI)"
    );
    parser.addOption(headlessOption);
    
    QCommandLineOption serverAddrOption(
        QStringList() << "s" << "server",
        "服务器地址 (默认: 127.0.0.1)",
        "address",
        "127.0.0.1"
    );
    parser.addOption(serverAddrOption);
    
    QCommandLineOption serverPortOption(
        QStringList() << "p" << "port",
        "服务器端口 (默认: 8888)",
        "port",
        "8888"
    );
    parser.addOption(serverPortOption);
    
    QCommandLineOption messageSizeOption(
        QStringList() << "m" << "message-size",
        "消息大小(字节) (默认: 1024)",
        "bytes",
        "1024"
    );
    parser.addOption(messageSizeOption);
    
    // 解析命令行参数
    parser.process(a);
    
    // 获取参数值
    QString configFile = parser.value(configOption);
    QString clientName = parser.value(clientNameOption);
    int testDuration = parser.value(testDurationOption).toInt();
    int sendInterval = parser.value(sendIntervalOption).toInt();
    bool headless = parser.isSet(headlessOption);
    QString serverAddr = parser.value(serverAddrOption);
    int serverPort = parser.value(serverPortOption).toInt();
    int messageSize = parser.value(messageSizeOption).toInt();
    
    // 验证配置文件是否存在
    if (!QDir().exists(configFile)) {
        qCritical() << "配置文件不存在:" << configFile;
        return 1;
    }
    
    // 输出启动信息
    qInfo() << "=== TCP客户端启动 ===";
    qInfo() << "配置文件:" << configFile;
    qInfo() << "客户端名称:" << clientName;
    qInfo() << "服务器地址:" << serverAddr << ":" << serverPort;
    qInfo() << "消息大小:" << messageSize << "字节";
    qInfo() << "测试持续时间:" << (testDuration > 0 ? QString("%1秒").arg(testDuration) : "无限制");
    qInfo() << "发送间隔:" << sendInterval << "微秒";
    qInfo() << "运行模式:" << (headless ? "无界面" : "GUI");
    
    // 创建主窗口并传递参数
    MainWindow w;
    
    // 设置窗口参数
    w.setConfigFile(configFile);
    w.setClientName(clientName);
    w.setServerAddress(serverAddr, serverPort);
    w.setSendInterval(sendInterval);
    w.setMessageSize(messageSize);
    
    // 根据模式决定是否显示窗口
    if (!headless) {
        w.show();
    } else {
        qInfo() << "无界面模式启动，窗口将不可见";
    }
    
    // 设置测试持续时间
    if (testDuration > 0) {
        QTimer::singleShot(testDuration * 1000, [&]() {
            qInfo() << "测试时间到达，程序即将退出";
            qInfo() << "客户端" << clientName << "测试完成";
            a.quit();
        });
        qInfo() << "测试定时器已设置:" << testDuration << "秒后自动退出";
    }
    
    return a.exec();
}
