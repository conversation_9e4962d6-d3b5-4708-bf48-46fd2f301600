#!/bin/bash
# TCP客户端压力测试脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查可执行文件
check_executable() {
    if [ ! -f "./build/tcp_qt5" ]; then
        print_error "找不到可执行文件 ./build/tcp_qt5"
        print_info "请先编译项目: ./build.sh"
        exit 1
    fi
}

# 运行单个测试用例
run_test_case() {
    local test_id=$1
    local test_name=$2
    local client_count=$3
    local message_size=$4
    local send_interval=$5
    local duration=$6
    
    print_info "开始测试用例 $test_id: $test_name"
    print_info "参数: $client_count个客户端, ${message_size}字节, ${send_interval}μs间隔, ${duration}秒"
    
    # 创建结果目录
    local result_dir="test_results/test_$test_id"
    mkdir -p "$result_dir"
    
    # 启动多个客户端
    local pids=()
    for ((i=1; i<=client_count; i++)); do
        local client_name="client_${test_id}_${i}"
        local log_file="$result_dir/${client_name}.log"
        
        print_info "启动客户端 $client_name"
        
        # 启动客户端进程（后台运行）
        ./build/tcp_qt5 \
            --headless \
            --client-name "$client_name" \
            --test-duration "$duration" \
            --send-interval "$send_interval" \
            --message-size "$message_size" \
            --server "127.0.0.1" \
            --port "8888" \
            > "$log_file" 2>&1 &
        
        local pid=$!
        pids+=($pid)
        echo "$pid" > "$result_dir/${client_name}.pid"
        
        # 稍微延迟避免同时启动
        sleep 0.1
    done
    
    print_info "已启动 ${#pids[@]} 个客户端，测试将运行 $duration 秒..."
    
    # 等待所有进程完成
    for pid in "${pids[@]}"; do
        wait $pid
    done
    
    print_info "测试用例 $test_id 完成"
    echo "----------------------------------------"
}

# 运行所有预定义的测试用例
run_all_tests() {
    print_info "开始运行所有测试用例"
    
    # 测试用例数组 (id:name:client_count:message_size:send_interval:duration)
    local test_cases=(
        "1:4线程10客户端128字节10μs间隔:10:128:10:180"
        "2:4线程1客户端128字节10μs间隔:1:128:10:180"
        "3:4线程5客户端128字节10μs间隔:5:128:10:180"
        "4:4线程10客户端128字节10μs间隔:10:128:10:180"
        "5:4线程1客户端128字节无间隔:1:128:0:180"
        "6:4线程5客户端128字节无间隔:5:128:0:180"
        "7:4线程10客户端128字节无间隔:10:128:0:180"
        "8:4线程1客户端128字节无间隔:1:128:0:180"
        "9:4线程5客户端128字节无间隔:5:128:0:180"
        "10:4线程10客户端128字节无间隔:10:128:0:180"
        "11:4线程1客户端256字节10μs间隔:1:256:10:180"
        "12:4线程5客户端256字节10μs间隔:5:256:10:180"
        "13:4线程10客户端256字节10μs间隔:10:256:10:180"
    )
    
    local total=${#test_cases[@]}
    local success=0
    
    for test_case in "${test_cases[@]}"; do
        IFS=':' read -r id name client_count message_size send_interval duration <<< "$test_case"
        
        if run_test_case "$id" "$name" "$client_count" "$message_size" "$send_interval" "$duration"; then
            ((success++))
        fi
        
        # 测试间隔
        if [ $id -lt $total ]; then
            print_info "等待5秒后开始下一个测试..."
            sleep 5
        fi
    done
    
    print_info "测试完成！成功: $success/$total"
}

# 运行指定测试用例
run_single_test() {
    local test_id=$1
    
    case $test_id in
        1) run_test_case "1" "4线程10客户端128字节10μs间隔" "10" "128" "10" "180" ;;
        2) run_test_case "2" "4线程1客户端128字节10μs间隔" "1" "128" "10" "180" ;;
        3) run_test_case "3" "4线程5客户端128字节10μs间隔" "5" "128" "10" "180" ;;
        4) run_test_case "4" "4线程10客户端128字节10μs间隔" "10" "128" "10" "180" ;;
        5) run_test_case "5" "4线程1客户端128字节无间隔" "1" "128" "0" "180" ;;
        6) run_test_case "6" "4线程5客户端128字节无间隔" "5" "128" "0" "180" ;;
        7) run_test_case "7" "4线程10客户端128字节无间隔" "10" "128" "0" "180" ;;
        8) run_test_case "8" "4线程1客户端128字节无间隔" "1" "128" "0" "180" ;;
        9) run_test_case "9" "4线程5客户端128字节无间隔" "5" "128" "0" "180" ;;
        10) run_test_case "10" "4线程10客户端128字节无间隔" "10" "128" "0" "180" ;;
        11) run_test_case "11" "4线程1客户端256字节10μs间隔" "1" "256" "10" "180" ;;
        12) run_test_case "12" "4线程5客户端256字节10μs间隔" "5" "256" "10" "180" ;;
        13) run_test_case "13" "4线程10客户端256字节10μs间隔" "10" "256" "10" "180" ;;
        *) print_error "无效的测试用例ID: $test_id" ;;
    esac
}

# 快速测试（短时间）
quick_test() {
    print_info "快速测试 - 每个测试只运行10秒"
    
    # 测试1个客户端
    run_test_case "quick1" "快速测试1客户端" "1" "128" "1000" "10"
    sleep 2
    
    # 测试3个客户端
    run_test_case "quick2" "快速测试3客户端" "3" "128" "1000" "10"
    
    print_info "快速测试完成！"
}

# 主菜单
main() {
    echo "========================================"
    echo "       TCP客户端压力测试工具"
    echo "========================================"
    
    # 检查可执行文件
    check_executable
    
    # 创建结果目录
    mkdir -p test_results
    
    echo ""
    echo "请选择运行模式:"
    echo "1. 快速测试 (1和3个客户端，各10秒)"
    echo "2. 运行所有测试用例 (13个测试，每个3分钟)"
    echo "3. 运行指定测试用例"
    echo "4. 自定义测试"
    echo "5. 退出"
    echo ""
    
    read -p "请输入选择 (1-5): " choice
    
    case $choice in
        1)
            quick_test
            ;;
        2)
            echo ""
            print_warn "这将运行13个测试用例，每个3分钟，总共约40分钟"
            read -p "确定要继续吗？(y/N): " confirm
            if [[ $confirm =~ ^[Yy]$ ]]; then
                run_all_tests
            else
                print_info "已取消"
            fi
            ;;
        3)
            echo ""
            echo "可用的测试用例:"
            echo "1-4:   不同客户端数量，128字节，10μs间隔"
            echo "5-10:  不同客户端数量，128字节，无间隔"
            echo "11-13: 不同客户端数量，256字节，10μs间隔"
            echo ""
            read -p "请输入测试用例ID (1-13): " test_id
            run_single_test "$test_id"
            ;;
        4)
            echo ""
            read -p "客户端数量: " client_count
            read -p "消息大小(字节): " message_size
            read -p "发送间隔(微秒，0表示无间隔): " send_interval
            read -p "持续时间(秒): " duration
            
            run_test_case "custom" "自定义测试" "$client_count" "$message_size" "$send_interval" "$duration"
            ;;
        5)
            print_info "退出"
            exit 0
            ;;
        *)
            print_error "无效选择"
            exit 1
            ;;
    esac
}

# 运行主程序
main "$@"
