#!/bin/bash
# 超简单的测试脚本

echo "TCP客户端测试"
echo "============"

# 检查程序是否存在
if [ ! -f "./build/tcp_qt5" ]; then
    echo "错误: 找不到程序文件"
    echo "请先编译: cd build && make"
    exit 1
fi

echo "请确保服务器在 127.0.0.1:8888 运行"
echo ""

# 创建结果目录
mkdir -p test_results

echo "选择测试:"
echo "1. 测试1个客户端"
echo "2. 测试3个客户端" 
echo "3. 测试5个客户端"
echo "4. 退出"
echo ""

read -p "请选择 (1-4): " choice

case $choice in
    1)
        echo "启动1个客户端，运行10秒..."
        ./build/tcp_qt5 --headless --client-name client1 --test-duration 10 --send-interval 1000 --message-size 128 --server 127.0.0.1 --port 8888
        echo "测试完成"
        ;;
    2)
        echo "启动3个客户端，运行10秒..."
        ./build/tcp_qt5 --headless --client-name client1 --test-duration 10 --send-interval 1000 --message-size 128 --server 127.0.0.1 --port 8888 > test_results/client1.log 2>&1 &
        ./build/tcp_qt5 --headless --client-name client2 --test-duration 10 --send-interval 1000 --message-size 128 --server 127.0.0.1 --port 8888 > test_results/client2.log 2>&1 &
        ./build/tcp_qt5 --headless --client-name client3 --test-duration 10 --send-interval 1000 --message-size 128 --server 127.0.0.1 --port 8888 > test_results/client3.log 2>&1 &
        
        echo "等待10秒..."
        sleep 11
        echo "测试完成，日志在 test_results/ 目录"
        ;;
    3)
        echo "启动5个客户端，运行10秒..."
        ./build/tcp_qt5 --headless --client-name client1 --test-duration 10 --send-interval 1000 --message-size 128 --server 127.0.0.1 --port 8888 > test_results/client1.log 2>&1 &
        ./build/tcp_qt5 --headless --client-name client2 --test-duration 10 --send-interval 1000 --message-size 128 --server 127.0.0.1 --port 8888 > test_results/client2.log 2>&1 &
        ./build/tcp_qt5 --headless --client-name client3 --test-duration 10 --send-interval 1000 --message-size 128 --server 127.0.0.1 --port 8888 > test_results/client3.log 2>&1 &
        ./build/tcp_qt5 --headless --client-name client4 --test-duration 10 --send-interval 1000 --message-size 128 --server 127.0.0.1 --port 8888 > test_results/client4.log 2>&1 &
        ./build/tcp_qt5 --headless --client-name client5 --test-duration 10 --send-interval 1000 --message-size 128 --server 127.0.0.1 --port 8888 > test_results/client5.log 2>&1 &
        
        echo "等待10秒..."
        sleep 11
        echo "测试完成，日志在 test_results/ 目录"
        ;;
    4)
        echo "退出"
        exit 0
        ;;
    *)
        echo "无效选择"
        exit 1
        ;;
esac

echo ""
echo "如果要查看日志: ls test_results/"
